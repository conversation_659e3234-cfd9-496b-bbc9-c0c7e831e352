import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/db/mongodb';
import { Report } from '@/lib/db/models';

export const runtime = 'nodejs';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ referenceNumber: string }> }
) {
  try {
    await connectDB();
    
    const { referenceNumber } = await params;
    
    if (!referenceNumber) {
      return NextResponse.json(
        { success: false, error: 'Reference number is required' },
        { status: 400 }
      );
    }

    // Find report by reference number
    const report = await Report.findOne({ 
      referenceNumber: referenceNumber.trim().toUpperCase() 
    }).populate('companyId', 'name');

    if (!report) {
      return NextResponse.json(
        { success: false, error: 'Report not found with this reference number' },
        { status: 404 }
      );
    }

    // Return limited information for tracking purposes
    const trackingInfo = {
      referenceNumber: report.referenceNumber,
      title: report.title,
      status: report.status,
      priority: report.priority,
      dateSubmitted: report.dateSubmitted,
      lastUpdated: report.lastUpdated,
      category: report.category,
      isAnonymous: report.isAnonymous,
      estimatedCompletion: report.estimatedCompletion,
      progress: report.progress || 0,
      company: report.companyId ? {
        name: report.companyId.name
      } : null,
      // Only include investigator info if not anonymous
      assignedInvestigator: report.isAnonymous ? null : report.assignedInvestigator,
      // Status history for tracking
      statusHistory: report.statusHistory || []
    };

    return NextResponse.json({
      success: true,
      data: trackingInfo
    });

  } catch (error) {
    console.error('Report tracking error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
