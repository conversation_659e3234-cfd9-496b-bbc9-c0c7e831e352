import NextAuth from 'next-auth';

declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      email: string;
      name?: string;
      image?: string;
      role: string;
      companyId?: string;
      firstName: string;
      lastName: string;
      provider: string;
    };
    customToken: string;
  }

  interface User {
    id: string;
    email: string;
    name?: string;
    image?: string;
    role: string;
    companyId?: string;
    firstName: string;
    lastName: string;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    id: string;
    role: string;
    companyId?: string;
    firstName: string;
    lastName: string;
    provider: string;
    customToken: string;
  }
}
