import { NextResponse } from 'next/server';
import { withCompanyIsolation, AuthenticatedRequest } from '@/lib/middleware/auth';
import connectDB from '@/lib/db/mongodb';
import User from '@/lib/db/models/User';
import bcrypt from 'bcrypt';

export const runtime = 'nodejs';

export const POST = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    if (!request.user) {
      return NextResponse.json(
        { success: false, error: 'User not authenticated' },
        { status: 401 }
      );
    }
    
    // Check if user is admin
    if (request.user.role !== 'admin' && request.user.role !== 'investigator') {
      return NextResponse.json(
        { success: false, error: 'Access denied. Admin privileges required.' },
        { status: 403 }
      );
    }
    
    const { currentPassword, newPassword } = await request.json();
    
    // Validate required fields
    if (!currentPassword || !newPassword) {
      return NextResponse.json(
        { success: false, error: 'Current password and new password are required' },
        { status: 400 }
      );
    }
    
    // Validate new password strength (stricter for admins)
    if (newPassword.length < 12) {
      return NextResponse.json(
        { success: false, error: 'Admin password must be at least 12 characters long' },
        { status: 400 }
      );
    }
    
    // Enhanced password strength validation for admins
    const hasUpperCase = /[A-Z]/.test(newPassword);
    const hasLowerCase = /[a-z]/.test(newPassword);
    const hasNumbers = /\d/.test(newPassword);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(newPassword);
    const hasMultipleSpecialChars = (newPassword.match(/[!@#$%^&*(),.?":{}|<>]/g) || []).length >= 2;
    
    if (!hasUpperCase || !hasLowerCase || !hasNumbers || !hasSpecialChar || !hasMultipleSpecialChars) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Admin password must contain uppercase, lowercase, numbers, and at least 2 special characters' 
        },
        { status: 400 }
      );
    }
    
    // Check for common patterns (no repeated characters, no sequential patterns)
    const hasRepeatedChars = /(.)\1{2,}/.test(newPassword);
    const hasSequentialChars = /(?:abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz|123|234|345|456|567|678|789)/i.test(newPassword);
    
    if (hasRepeatedChars || hasSequentialChars) {
      return NextResponse.json(
        { success: false, error: 'Password cannot contain repeated or sequential characters' },
        { status: 400 }
      );
    }
    
    // Get user with password
    const user = await User.findById(request.user.id);
    
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }
    
    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.hashedPassword);
    
    if (!isCurrentPasswordValid) {
      return NextResponse.json(
        { success: false, error: 'Current password is incorrect' },
        { status: 400 }
      );
    }
    
    // Check if new password is different from current
    const isSamePassword = await bcrypt.compare(newPassword, user.hashedPassword);
    
    if (isSamePassword) {
      return NextResponse.json(
        { success: false, error: 'New password must be different from current password' },
        { status: 400 }
      );
    }
    
    // Hash new password with higher salt rounds for admins
    const saltRounds = 14;
    const hashedNewPassword = await bcrypt.hash(newPassword, saltRounds);
    
    // Update password
    await User.findByIdAndUpdate(request.user.id, {
      hashedPassword: hashedNewPassword,
      passwordHashAlgorithm: 'bcrypt',
      passwordNeedsMigration: false,
      // Reset failed login attempts on successful password change
      failedLoginAttempts: 0,
      accountLocked: false,
      accountLockedUntil: undefined
    });
    
    // Log password change for audit purposes
    console.log(`Admin password changed for user ${user.email} at ${new Date().toISOString()}`);
    
    return NextResponse.json({
      success: true,
      message: 'Admin password changed successfully'
    });
  } catch (error) {
    console.error('Change admin password API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
});
