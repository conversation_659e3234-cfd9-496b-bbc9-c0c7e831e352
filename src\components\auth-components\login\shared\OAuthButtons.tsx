"use client";

import { Button } from "@/components/ui/button";
import { signIn } from "next-auth/react";
import Image from "next/image";
import { useState } from "react";
import { toast } from "sonner";

interface OAuthButtonsProps {
  callbackUrl?: string;
  role?: 'admin' | 'whistleblower';
}

export default function OAuthButtons({ callbackUrl = "/dashboard", role }: OAuthButtonsProps) {
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);
  const [isMicrosoftLoading, setIsMicrosoftLoading] = useState(false);

  const handleGoogleSignIn = async () => {
    try {
      setIsGoogleLoading(true);
      const result = await signIn("google", {
        callbackUrl,
        redirect: false
      });

      if (result?.error) {
        toast.error("Failed to sign in with Google. Please try again.");
        console.error("Google sign in error:", result.error);
      } else if (result?.url) {
        // Successful sign-in, redirect will happen automatically
        window.location.href = result.url;
      }
    } catch (error) {
      toast.error("An error occurred during Google sign-in.");
      console.error("Google sign in error:", error);
    } finally {
      setIsGoogleLoading(false);
    }
  };

  const handleMicrosoftSignIn = async () => {
    try {
      setIsMicrosoftLoading(true);
      const result = await signIn("azure-ad", {
        callbackUrl,
        redirect: false
      });

      if (result?.error) {
        toast.error("Failed to sign in with Microsoft. Please try again.");
        console.error("Microsoft sign in error:", result.error);
      } else if (result?.url) {
        // Successful sign-in, redirect will happen automatically
        window.location.href = result.url;
      }
    } catch (error) {
      toast.error("An error occurred during Microsoft sign-in.");
      console.error("Microsoft sign in error:", error);
    } finally {
      setIsMicrosoftLoading(false);
    }
  };

  return (
    <div className="space-y-3 my-4">
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-background px-2 text-muted-foreground">
            Or continue with
          </span>
        </div>
      </div>

      <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2">
        <Button
          onClick={handleGoogleSignIn}
          disabled={isGoogleLoading || isMicrosoftLoading}
          className="flex-1 flex items-center justify-center gap-2 bg-transparent h-12 border-gray-300 hover:bg-gray-50"
          variant="outline"
          type="button"
        >
          {isGoogleLoading ? (
            <div className="h-5 w-5 animate-spin rounded-full border-2 border-gray-300 border-t-black" />
          ) : (
            <svg className="h-5 w-5" viewBox="0 0 24 24">
              <path
                fill="#4285F4"
                d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
              />
              <path
                fill="#34A853"
                d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
              />
              <path
                fill="#FBBC05"
                d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
              />
              <path
                fill="#EA4335"
                d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
              />
            </svg>
          )}
          <span className="text-sm font-medium">Google</span>
        </Button>

        <Button
          onClick={handleMicrosoftSignIn}
          disabled={isGoogleLoading || isMicrosoftLoading}
          className="flex-1 flex items-center justify-center gap-2 bg-transparent h-12 border-gray-300 hover:bg-gray-50"
          variant="outline"
          type="button"
        >
          {isMicrosoftLoading ? (
            <div className="h-5 w-5 animate-spin rounded-full border-2 border-gray-300 border-t-black" />
          ) : (
            <svg className="h-5 w-5" viewBox="0 0 24 24">
              <path fill="#f25022" d="M1 1h10v10H1z"/>
              <path fill="#00a4ef" d="M13 1h10v10H13z"/>
              <path fill="#7fba00" d="M1 13h10v10H1z"/>
              <path fill="#ffb900" d="M13 13h10v10H13z"/>
            </svg>
          )}
          <span className="text-sm font-medium">Microsoft</span>
        </Button>
      </div>
    </div>
  );
}