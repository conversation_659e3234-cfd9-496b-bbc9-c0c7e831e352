import { config } from 'dotenv';
import mongoose from 'mongoose';
import bcrypt from 'bcrypt';
import connectDB from '../src/lib/db/mongodb';
import { User, Company, Report, Notification, Conversation, Message, Blog, PricingPlan } from '../src/lib/db/models';
import { encryptMessage } from '../src/lib/encryption/messageEncryption';

config({ path: '.env.local' });

// Sample companies data
const companies = [
  {
    name: 'TechCorp Industries',
    domain: 'techcorp.com',
    industry: 'Technology',
    size: 'Large',
    address: {
      street: '123 Tech Street',
      city: 'San Francisco',
      state: 'CA',
      zipCode: '94105',
      country: 'USA'
    },
    contactInfo: {
      phone: '******-0123',
      email: '<EMAIL>',
      website: 'https://techcorp.com'
    },
    subscription: {
      plan: 'enterprise',
      status: 'active',
      startDate: new Date('2024-01-01'),
      endDate: new Date('2025-01-01'),
      maxUsers: 1000,
      features: ['advanced_analytics', 'custom_branding', 'api_access']
    },
    settings: {
      allowAnonymousReports: true,
      requireApproval: false,
      retentionPeriod: 2555,
      encryptionEnabled: true
    },
    isActive: true
  },
  {
    name: 'Global Manufacturing Co',
    domain: 'globalmfg.com',
    industry: 'Manufacturing',
    size: 'Large',
    address: {
      street: '456 Industrial Blvd',
      city: 'Detroit',
      state: 'MI',
      zipCode: '48201',
      country: 'USA'
    },
    contactInfo: {
      phone: '******-0456',
      email: '<EMAIL>',
      website: 'https://globalmfg.com'
    },
    subscription: {
      plan: 'professional',
      status: 'active',
      startDate: new Date('2024-02-01'),
      endDate: new Date('2025-02-01'),
      maxUsers: 500,
      features: ['analytics', 'custom_workflows']
    },
    settings: {
      allowAnonymousReports: true,
      requireApproval: true,
      retentionPeriod: 1825,
      encryptionEnabled: true
    },
    isActive: true
  }
];

// Sample users data with proper structure
const usersData = [
  // TechCorp users
  {
    email: '<EMAIL>',
    firstName: 'Emily',
    lastName: 'Johnson',
    role: 'admin',
    password: 'admin123',
    companyIndex: 0
  },
  {
    email: '<EMAIL>',
    firstName: 'Alexandra',
    lastName: 'Smith',
    role: 'investigator',
    password: 'investigator123',
    companyIndex: 0
  },
  {
    email: '<EMAIL>',
    firstName: 'Michael',
    lastName: 'Johnson',
    role: 'investigator',
    password: 'investigator123',
    companyIndex: 0
  },
  {
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Doe',
    role: 'whistleblower',
    password: 'employee123',
    companyIndex: 0
  },
  // Global Manufacturing users
  {
    email: '<EMAIL>',
    firstName: 'Robert',
    lastName: 'Wilson',
    role: 'admin',
    password: 'admin123',
    companyIndex: 1
  },
  {
    email: '<EMAIL>',
    firstName: 'David',
    lastName: 'Thompson',
    role: 'investigator',
    password: 'investigator123',
    companyIndex: 1
  }
];

// Sample reports data
const reportsData = [
  {
    title: 'Potential accounting irregularities in Q1 reports',
    description: 'Observed discrepancies in financial reporting that may indicate fraudulent activity. Multiple entries show inconsistent patterns and unexplained variances.',
    category: 'Financial',
    priority: 'High',
    status: 'Under Review',
    isAnonymous: false,
    incidentDate: new Date('2025-01-08'),
    location: 'Accounting Department, Floor 3',
    tags: ['financial', 'fraud', 'urgent'],
    userEmail: '<EMAIL>',
    assignedInvestigatorEmail: '<EMAIL>'
  },
  {
    title: 'Workplace safety concerns in manufacturing plant B',
    description: 'Multiple safety violations observed that could lead to serious injuries. Equipment guards are missing and safety protocols are not being followed.',
    category: 'Workplace Safety',
    priority: 'Critical',
    status: 'New',
    isAnonymous: false,
    incidentDate: new Date('2025-01-01'),
    location: 'Manufacturing Plant B',
    tags: ['safety', 'manufacturing', 'critical'],
    userEmail: '<EMAIL>'
  }
];

// Sample pricing plans
const pricingPlans = [
  {
    name: 'Starter',
    price: 99,
    features: [
      'Secure anonymous reporting',
      'Basic case management tools',
      'Automated case tracking & audit logs',
      'Customizable intake forms',
      'Whistleblower two-way messaging',
      'Basic analytics dashboard'
    ],
    order: 1,
    isActive: true
  },
  {
    name: 'Professional',
    price: 169,
    features: [
      'Everything in Starter plus:',
      'Advanced case routing & AI risk scoring',
      'Custom workflows & approval processes',
      'Surveys & employee engagement tools',
      'Automated fraud detection & Analysis',
      'Customizable alerts & notifications',
      'HR & compliance assistant'
    ],
    order: 2,
    isActive: true
  },
  {
    name: 'Enterprise',
    price: 499,
    features: [
      'Everything in Professional plus:',
      'Phone hotline & multilingual support',
      'Unlimited investigators & case managers',
      'Custom server locations',
      'Dedicated success manager',
      'Enterprise-level compliance',
      'Advanced analytics & reporting'
    ],
    order: 3,
    isActive: true
  }
];

// Sample blog posts
const blogPosts = [
  {
    image: '/desktop/blog/featured/f-blog1.jpg',
    category: 'Compliance Updates',
    date: 'June 1, 2025',
    title: 'New EU Whistleblower Protection Directive: What Organizations Need to Know',
    description: 'The latest updates to the EU Whistleblower Protection Directive bring significant changes to compliance requirements.',
    author: {
      image: '/desktop/blog/authors/Dr. Elizabeth Morgan.jpg',
      name: 'Dr. Elizabeth Morgan',
      initials: 'EM'
    },
    readTime: '8 min read',
    slug: 'new-eu-whistleblower-protection-directive',
    featured: true,
    tags: ['EU Directive', 'Whistleblower Protection', 'Compliance', 'Regulatory Updates'],
    content: 'The European Union has recently introduced significant updates to the Whistleblower Protection Directive...'
  }
];

async function clearDatabase() {
  console.log('🧹 Clearing existing data...');
  await Promise.all([
    Company.deleteMany({}),
    User.deleteMany({}),
    Report.deleteMany({}),
    Notification.deleteMany({}),
    Conversation.deleteMany({}),
    Message.deleteMany({}),
    Blog.deleteMany({}),
    PricingPlan.deleteMany({})
  ]);
  console.log('✅ Database cleared');
}

async function seedCompanies() {
  console.log('🏢 Seeding companies...');
  const createdCompanies = await Company.insertMany(companies);
  console.log(`✅ Created ${createdCompanies.length} companies`);
  return createdCompanies;
}

async function seedUsers(createdCompanies: any[]) {
  console.log('👥 Seeding users...');
  const users = [];
  
  for (const userData of usersData) {
    const hashedPassword = await bcrypt.hash(userData.password, 12);
    const companyId = createdCompanies[userData.companyIndex]._id;
    
    users.push({
      email: userData.email,
      firstName: userData.firstName,
      lastName: userData.lastName,
      role: userData.role,
      companyId,
      hashedPassword,
      isActive: true,
      emailVerified: true,
      lastLogin: new Date(),
      lastActive: new Date(),
      preferences: {
        language: 'en',
        notifications: {
          email: true,
          push: true,
          sms: false
        },
        theme: 'light'
      }
    });
  }
  
  const createdUsers = await User.insertMany(users);
  console.log(`✅ Created ${createdUsers.length} users`);
  return createdUsers;
}

async function seedReports(createdUsers: any[], createdCompanies: any[]) {
  console.log('📋 Seeding reports...');
  const reports = [];
  
  for (const reportData of reportsData) {
    const user = createdUsers.find(u => u.email === reportData.userEmail);
    const investigator = reportData.assignedInvestigatorEmail 
      ? createdUsers.find(u => u.email === reportData.assignedInvestigatorEmail)
      : null;
    
    if (user) {
      const reportId = `WB-2025-${String(reports.length + 1).padStart(4, '0')}`;
      
      reports.push({
        reportId,
        userId: user._id,
        companyId: user.companyId,
        title: reportData.title,
        description: reportData.description,
        category: reportData.category,
        priority: reportData.priority,
        status: reportData.status,
        isAnonymous: reportData.isAnonymous,
        progress: reportData.status === 'Under Review' ? 45 : 0,
        assignedInvestigator: investigator?._id,
        incidentDate: reportData.incidentDate,
        location: reportData.location,
        tags: reportData.tags,
        metadata: {
          submissionMethod: 'web',
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });
    }
  }
  
  const createdReports = await Report.insertMany(reports);
  console.log(`✅ Created ${createdReports.length} reports`);
  return createdReports;
}

async function seedConversationsAndMessages(createdReports: any[], createdUsers: any[]) {
  console.log('💬 Seeding conversations and messages...');
  
  for (const report of createdReports) {
    const user = createdUsers.find(u => u._id.toString() === report.userId.toString());
    const admin = createdUsers.find(u => u.companyId.toString() === user.companyId.toString() && u.role === 'admin');
    const investigator = report.assignedInvestigator 
      ? createdUsers.find(u => u._id.toString() === report.assignedInvestigator.toString())
      : null;
    
    // Create admin-user conversation
    const adminConversation = await Conversation.create({
      reportId: report._id,
      participants: [user._id, admin._id],
      status: 'active',
      isEncrypted: true
    });
    
    // Admin confirmation message
    const adminMessage = `Thank you for your report "${report.title}". We have received your submission and will begin our investigation shortly. Your report ID is ${report.reportId}.`;
    const encryptedAdminMessage = encryptMessage(adminMessage);
    
    await Message.create({
      conversationId: adminConversation._id,
      senderId: admin._id,
      content: encryptedAdminMessage.encryptedContent,
      messageType: 'text',
      isEncrypted: true,
      encryptionData: {
        contentIv: encryptedAdminMessage.iv,
        contentTag: encryptedAdminMessage.tag
      },
      readBy: []
    });
    
    // User response
    const userMessage = 'Thank you for the quick response. I have additional evidence that I can provide if needed.';
    const encryptedUserMessage = encryptMessage(userMessage);
    
    await Message.create({
      conversationId: adminConversation._id,
      senderId: user._id,
      content: encryptedUserMessage.encryptedContent,
      messageType: 'text',
      isEncrypted: true,
      encryptionData: {
        contentIv: encryptedUserMessage.iv,
        contentTag: encryptedUserMessage.tag
      },
      readBy: [{
        userId: admin._id,
        readAt: new Date()
      }]
    });
    
    // Create investigator conversation if assigned
    if (investigator) {
      const investigatorConversation = await Conversation.create({
        reportId: report._id,
        participants: [user._id, investigator._id],
        status: 'active',
        isEncrypted: true
      });
      
      const investigatorMessage = `Hello, I'm ${investigator.firstName} ${investigator.lastName}, and I've been assigned as the investigator for your report "${report.title}" (${report.reportId}). I will be handling your case from this point forward.`;
      const encryptedInvestigatorMessage = encryptMessage(investigatorMessage);
      
      await Message.create({
        conversationId: investigatorConversation._id,
        senderId: investigator._id,
        content: encryptedInvestigatorMessage.encryptedContent,
        messageType: 'text',
        isEncrypted: true,
        encryptionData: {
          contentIv: encryptedInvestigatorMessage.iv,
          contentTag: encryptedInvestigatorMessage.tag
        },
        readBy: []
      });
    }
  }
  
  const conversationCount = await Conversation.countDocuments();
  const messageCount = await Message.countDocuments();
  console.log(`✅ Created ${conversationCount} conversations and ${messageCount} messages`);
}

async function seedNotifications(createdUsers: any[], createdReports: any[]) {
  console.log('🔔 Seeding notifications...');
  const notifications = [];
  
  for (const user of createdUsers) {
    // Welcome notification
    notifications.push({
      userId: user._id,
      type: 'system',
      title: 'Welcome to 7IRIS Platform',
      message: `Welcome ${user.firstName}! Your account has been successfully created and you can now access all platform features.`,
      priority: 'medium',
      status: 'unread',
      actionUrl: user.role === 'whistleblower' ? '/dashboard/whistleblower' : '/dashboard/admin',
      metadata: {
        source: 'system',
        category: 'welcome'
      }
    });
    
    // Role-specific notifications
    if (user.role === 'whistleblower') {
      const userReports = createdReports.filter(r => r.userId.toString() === user._id.toString());
      for (const report of userReports) {
        notifications.push({
          userId: user._id,
          type: 'report_update',
          title: 'Report Received',
          message: `Your report "${report.title}" has been received and assigned ID ${report.reportId}.`,
          priority: 'medium',
          status: 'unread',
          reportId: report._id,
          actionUrl: '/dashboard/whistleblower/my-reports',
          metadata: {
            source: 'system',
            category: 'report_confirmation'
          }
        });
      }
    }
  }
  
  const createdNotifications = await Notification.insertMany(notifications);
  console.log(`✅ Created ${createdNotifications.length} notifications`);
}

async function seedPricingPlans() {
  console.log('💰 Seeding pricing plans...');
  const plansWithTimestamps = pricingPlans.map(plan => ({
    ...plan,
    createdAt: new Date(),
    updatedAt: new Date()
  }));
  const createdPlans = await PricingPlan.insertMany(plansWithTimestamps);
  console.log(`✅ Created ${createdPlans.length} pricing plans`);
}

async function seedBlogPosts() {
  console.log('📝 Seeding blog posts...');
  const postsWithTimestamps = blogPosts.map(post => ({
    ...post,
    createdAt: new Date(),
    updatedAt: new Date()
  }));
  const createdPosts = await Blog.insertMany(postsWithTimestamps);
  console.log(`✅ Created ${createdPosts.length} blog posts`);
}

export async function seedDatabase() {
  try {
    console.log('🌱 Starting database seeding...');
    
    await connectDB();
    console.log('✅ Connected to database');

    await clearDatabase();
    
    const createdCompanies = await seedCompanies();
    const createdUsers = await seedUsers(createdCompanies);
    const createdReports = await seedReports(createdUsers, createdCompanies);
    
    await seedConversationsAndMessages(createdReports, createdUsers);
    await seedNotifications(createdUsers, createdReports);
    await seedPricingPlans();
    await seedBlogPosts();

    console.log('\n🎉 Database seeding completed successfully!');
    console.log('\n📊 Summary:');
    console.log(`   Companies: ${createdCompanies.length}`);
    console.log(`   Users: ${createdUsers.length}`);
    console.log(`   Reports: ${createdReports.length}`);
    console.log(`   Conversations: ${await Conversation.countDocuments()}`);
    console.log(`   Messages: ${await Message.countDocuments()}`);
    console.log(`   Notifications: ${await Notification.countDocuments()}`);
    console.log(`   Pricing Plans: ${pricingPlans.length}`);
    console.log(`   Blog Posts: ${blogPosts.length}`);
    
    console.log('\n🔑 Test Credentials:');
    console.log('   Admin: <EMAIL> / admin123');
    console.log('   Investigator: <EMAIL> / investigator123');
    console.log('   Whistleblower: <EMAIL> / employee123');

    return {
      companies: createdCompanies.length,
      users: createdUsers.length,
      reports: createdReports.length,
      message: 'Database seeding completed successfully'
    };
  } catch (error) {
    console.error('❌ Error seeding database:', error);
    throw error;
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
  }
}

// Run if executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  seedDatabase()
    .then(() => {
      console.log('✅ Seeding process completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Seeding process failed:', error);
      process.exit(1);
    });
}