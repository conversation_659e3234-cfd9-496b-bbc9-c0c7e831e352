import logger from '@/lib/utils/logger';

/**
 * Role Assignment Service for OAuth Users
 * Determines the appropriate role for new OAuth users based on various criteria
 */

export interface RoleAssignmentCriteria {
  email: string;
  domain: string;
  name?: string;
  provider: string;
}

export interface RoleAssignmentResult {
  role: 'admin' | 'investigator' | 'whistleblower';
  reason: string;
  requiresApproval?: boolean;
}

/**
 * Admin email domains - users from these domains get admin access
 */
const ADMIN_DOMAINS = [
  'admin.company.com',
  'management.company.com',
  // Add your organization's admin domains here
];

/**
 * Investigator email domains - users from these domains get investigator access
 */
const INVESTIGATOR_DOMAINS = [
  'hr.company.com',
  'compliance.company.com',
  'legal.company.com',
  // Add your organization's investigator domains here
];

/**
 * Admin email addresses - specific emails that should have admin access
 */
const ADMIN_EMAILS = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  // Add specific admin email addresses here
];

/**
 * Investigator email addresses - specific emails that should have investigator access
 */
const INVESTIGATOR_EMAILS = [
  '<EMAIL>',
  '<EMAIL>',
  // Add specific investigator email addresses here
];

/**
 * Assigns a role to a new OAuth user based on email domain and other criteria
 */
export function assignRoleToOAuthUser(criteria: RoleAssignmentCriteria): RoleAssignmentResult {
  const { email, domain, name, provider } = criteria;
  
  logger.info('Assigning role to OAuth user', { email, domain, provider });

  // Check for specific admin emails
  if (ADMIN_EMAILS.includes(email.toLowerCase())) {
    return {
      role: 'admin',
      reason: 'Email address is in admin whitelist',
      requiresApproval: false
    };
  }

  // Check for specific investigator emails
  if (INVESTIGATOR_EMAILS.includes(email.toLowerCase())) {
    return {
      role: 'investigator',
      reason: 'Email address is in investigator whitelist',
      requiresApproval: false
    };
  }

  // Check admin domains
  if (ADMIN_DOMAINS.includes(domain.toLowerCase())) {
    return {
      role: 'admin',
      reason: `Email domain ${domain} is configured for admin access`,
      requiresApproval: true // Require approval for domain-based admin access
    };
  }

  // Check investigator domains
  if (INVESTIGATOR_DOMAINS.includes(domain.toLowerCase())) {
    return {
      role: 'investigator',
      reason: `Email domain ${domain} is configured for investigator access`,
      requiresApproval: false
    };
  }

  // Check for common admin patterns in email
  const emailLower = email.toLowerCase();
  const adminPatterns = [
    'admin',
    'administrator',
    'ceo',
    'cto',
    'cfo',
    'president',
    'director',
    'manager',
    'compliance',
    'legal'
  ];

  for (const pattern of adminPatterns) {
    if (emailLower.includes(pattern)) {
      return {
        role: 'investigator', // Conservative approach - give investigator role, not admin
        reason: `Email contains admin-related keyword: ${pattern}`,
        requiresApproval: true
      };
    }
  }

  // Default role for all other users
  return {
    role: 'whistleblower',
    reason: 'Default role for new OAuth users',
    requiresApproval: false
  };
}

/**
 * Validates if a user should be allowed to sign in based on domain restrictions
 */
export function validateOAuthUserDomain(email: string, provider: string): {
  allowed: boolean;
  reason: string;
} {
  const domain = email.split('@')[1]?.toLowerCase();
  
  if (!domain) {
    return {
      allowed: false,
      reason: 'Invalid email format'
    };
  }

  // Add domain restrictions here if needed
  const BLOCKED_DOMAINS = [
    'tempmail.com',
    '10minutemail.com',
    'guerrillamail.com',
    // Add domains you want to block
  ];

  if (BLOCKED_DOMAINS.includes(domain)) {
    return {
      allowed: false,
      reason: `Domain ${domain} is not allowed for registration`
    };
  }

  // For enterprise deployments, you might want to restrict to specific domains
  const ALLOWED_DOMAINS = process.env.OAUTH_ALLOWED_DOMAINS?.split(',').map(d => d.trim().toLowerCase()) || [];
  
  if (ALLOWED_DOMAINS.length > 0 && !ALLOWED_DOMAINS.includes(domain)) {
    return {
      allowed: false,
      reason: `Domain ${domain} is not in the allowed domains list`
    };
  }

  return {
    allowed: true,
    reason: 'Domain validation passed'
  };
}

/**
 * Determines if a user needs manual approval before being activated
 */
export function requiresManualApproval(
  role: string,
  provider: string,
  email: string
): boolean {
  // Admin users always require approval
  if (role === 'admin') {
    return true;
  }

  // Check if manual approval is required for this provider
  const requireApprovalForProvider = process.env.OAUTH_REQUIRE_APPROVAL?.split(',').includes(provider) || false;
  
  if (requireApprovalForProvider) {
    return true;
  }

  // Check if manual approval is required for all OAuth users
  const requireApprovalForAll = process.env.OAUTH_REQUIRE_APPROVAL_ALL === 'true';
  
  return requireApprovalForAll;
}

/**
 * Gets the appropriate dashboard URL for a user role
 */
export function getDashboardUrlForRole(role: string): string {
  switch (role) {
    case 'admin':
      return '/dashboard/admin';
    case 'investigator':
      return '/dashboard/admin'; // Investigators use admin dashboard
    case 'whistleblower':
    default:
      return '/dashboard/whistleblower';
  }
}

/**
 * Configuration for OAuth role assignment
 */
export const OAUTH_ROLE_CONFIG = {
  defaultRole: 'whistleblower' as const,
  requireApprovalForAdmin: true,
  requireApprovalForInvestigator: false,
  allowedProviders: ['google', 'azure-ad'],
  maxFailedAttempts: 3,
  lockoutDuration: 15 * 60 * 1000, // 15 minutes
};
