"use client";

import React from 'react';
import { cn } from '@/lib/utils';
import { Check } from 'lucide-react';

export interface Step {
  id: string;
  title: string;
  description?: string;
  status: 'upcoming' | 'current' | 'complete';
}

interface StepIndicatorProps {
  steps: Step[];
  currentStep: number;
  className?: string;
}

export function StepIndicator({ steps, currentStep, className }: StepIndicatorProps) {
  return (
    <div className={cn("w-full", className)}>
      <div className="flex items-center justify-between">
        {steps.map((step, index) => {
          const isCompleted = index < currentStep;
          const isCurrent = index === currentStep;
          const isUpcoming = index > currentStep;

          return (
            <React.Fragment key={step.id}>
              <div className="flex flex-col items-center">
                {/* Step Circle */}
                <div
                  className={cn(
                    "flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium transition-all duration-300",
                    {
                      "bg-green-600 text-white": isCompleted,
                      "bg-[#1E4841] text-white": isCurrent,
                      "bg-gray-300 text-gray-500": isUpcoming,
                    }
                  )}
                >
                  {isCompleted ? (
                    <Check className="w-4 h-4" />
                  ) : (
                    <span>{index + 1}</span>
                  )}
                </div>
                
                {/* Step Label */}
                <div className="mt-2 text-center">
                  <p
                    className={cn(
                      "text-xs sm:text-sm font-medium transition-colors duration-300",
                      {
                        "text-green-600": isCompleted,
                        "text-[#1E4841]": isCurrent,
                        "text-gray-500": isUpcoming,
                      }
                    )}
                  >
                    {step.title}
                  </p>
                  {step.description && (
                    <p className="text-xs text-gray-400 mt-1 max-w-20 sm:max-w-24">
                      {step.description}
                    </p>
                  )}
                </div>
              </div>

              {/* Connector Line */}
              {index < steps.length - 1 && (
                <div
                  className={cn(
                    "flex-1 h-1 mx-2 sm:mx-4 transition-colors duration-300",
                    {
                      "bg-green-600": index < currentStep,
                      "bg-gray-300": index >= currentStep,
                    }
                  )}
                />
              )}
            </React.Fragment>
          );
        })}
      </div>
    </div>
  );
}

interface StepNavigationProps {
  currentStep: number;
  totalSteps: number;
  onNext?: () => void;
  onPrevious?: () => void;
  onSubmit?: () => void;
  nextLabel?: string;
  previousLabel?: string;
  submitLabel?: string;
  isLoading?: boolean;
  canProceed?: boolean;
  showSaveDraft?: boolean;
  onSaveDraft?: () => void;
  className?: string;
}

export function StepNavigation({
  currentStep,
  totalSteps,
  onNext,
  onPrevious,
  onSubmit,
  nextLabel = "Continue",
  previousLabel = "Back",
  submitLabel = "Submit",
  isLoading = false,
  canProceed = true,
  showSaveDraft = false,
  onSaveDraft,
  className,
}: StepNavigationProps) {
  const isFirstStep = currentStep === 0;
  const isLastStep = currentStep === totalSteps - 1;

  return (
    <div className={cn("flex flex-col sm:flex-row gap-4 justify-between", className)}>
      {/* Previous Button */}
      <div className="flex">
        {!isFirstStep && onPrevious && (
          <button
            type="button"
            onClick={onPrevious}
            disabled={isLoading}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#1E4841] disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
          >
            ← {previousLabel}
          </button>
        )}
      </div>

      {/* Next/Submit and Save Draft Buttons */}
      <div className="flex flex-col sm:flex-row gap-4">
        {showSaveDraft && onSaveDraft && (
          <button
            type="button"
            onClick={onSaveDraft}
            disabled={isLoading}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
          >
            Save as Draft
          </button>
        )}
        
        {isLastStep ? (
          onSubmit && (
            <button
              type="button"
              onClick={onSubmit}
              disabled={isLoading || !canProceed}
              className="px-6 py-2 text-sm font-medium text-white bg-[#1E4841] border border-transparent rounded-md hover:bg-[#2A5D54] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#1E4841] disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            >
              {isLoading ? "Processing..." : submitLabel}
            </button>
          )
        ) : (
          onNext && (
            <button
              type="button"
              onClick={onNext}
              disabled={isLoading || !canProceed}
              className="px-6 py-2 text-sm font-medium text-white bg-[#1E4841] border border-transparent rounded-md hover:bg-[#2A5D54] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#1E4841] disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            >
              {isLoading ? "Processing..." : nextLabel}
            </button>
          )
        )}
      </div>
    </div>
  );
}
