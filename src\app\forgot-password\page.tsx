"use client";

import React, { useState } from 'react';
import <PERSON> from 'next/link';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ArrowLeft, Mail, CheckCircle } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import Image from 'next/image';

export default function ForgotPasswordPage() {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email.trim()) {
      toast({
        title: "Email Required",
        description: "Please enter your email address",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('/api/auth/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: email.trim() }),
      });

      const result = await response.json();

      if (result.success) {
        setIsSubmitted(true);
        toast({
          title: "Reset Email Sent",
          description: "If an account exists with this email, you will receive password reset instructions."
        });
      } else {
        // Always show success message for security reasons
        setIsSubmitted(true);
        toast({
          title: "Reset Email Sent",
          description: "If an account exists with this email, you will receive password reset instructions."
        });
      }
    } catch (error) {
      console.error('Forgot password error:', error);
      toast({
        title: "Error",
        description: "Something went wrong. Please try again later.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* Header */}
        <div className="text-center">
          <Link href="/" className="inline-block mb-6">
            <Image src="/logo.svg" alt="IRIS Logo" width={60} height={60} className="mx-auto" />
          </Link>
          <h2 className="text-3xl font-bold text-gray-900">
            {isSubmitted ? 'Check Your Email' : 'Forgot Password'}
          </h2>
          <p className="mt-2 text-gray-600">
            {isSubmitted 
              ? 'We\'ve sent password reset instructions to your email address'
              : 'Enter your email address and we\'ll send you a link to reset your password'
            }
          </p>
        </div>

        <Card>
          <CardContent className="pt-6">
            {isSubmitted ? (
              <div className="text-center space-y-4">
                <CheckCircle className="h-16 w-16 text-green-500 mx-auto" />
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Reset Instructions Sent
                  </h3>
                  <p className="text-gray-600 mb-4">
                    If an account exists with <strong>{email}</strong>, you will receive an email with instructions to reset your password.
                  </p>
                  <p className="text-sm text-gray-500">
                    Didn't receive an email? Check your spam folder or try again with a different email address.
                  </p>
                </div>
                <div className="space-y-3">
                  <Button 
                    onClick={() => {
                      setIsSubmitted(false);
                      setEmail('');
                    }}
                    variant="outline" 
                    className="w-full"
                  >
                    Try Different Email
                  </Button>
                  <Link href="/login/whistleblower" className="block">
                    <Button className="w-full bg-[#1E4841] hover:bg-[#1E4841]/90">
                      Back to Login
                    </Button>
                  </Link>
                </div>
              </div>
            ) : (
              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <Label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                    Email Address
                  </Label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                    <Input
                      id="email"
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="pl-10 h-12"
                      placeholder="Enter your email address"
                      required
                    />
                  </div>
                </div>

                <Button
                  type="submit"
                  disabled={isLoading}
                  className="w-full bg-[#1E4841] hover:bg-[#1E4841]/90 h-12 text-base font-semibold"
                >
                  {isLoading ? 'Sending...' : 'Send Reset Instructions'}
                </Button>
              </form>
            )}
          </CardContent>
        </Card>

        {/* Back to Login */}
        {!isSubmitted && (
          <div className="text-center">
            <Link href="/login/whistleblower" className="inline-flex items-center text-[#1E4841] hover:underline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Login
            </Link>
          </div>
        )}

        {/* Help Text */}
        <div className="text-center text-sm text-gray-500">
          <p>
            Need help? Contact our support team at{' '}
            <a href="mailto:<EMAIL>" className="text-[#1E4841] hover:underline">
              <EMAIL>
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}
