import { Schema } from 'mongoose';
import { createModel } from '../utils';

const EvidenceFileSchema = new Schema({
  fileName: { type: String, required: true },
  originalName: { type: String, required: true },
  fileSize: { type: Number, required: true },
  mimeType: { type: String, required: true },
  fileUrl: { type: String, required: true },
  uploadedAt: { type: Date, default: Date.now },
  uploadedBy: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  description: { type: String },
  isEncrypted: { type: Boolean, default: false }
});

const ReportSchema = new Schema({
  reportId: { type: String, required: true, unique: true },
  referenceNumber: { type: String, unique: true, sparse: true }, // For tracking reports
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  companyId: { type: Schema.Types.ObjectId, ref: 'Company' },

  // Step 1 - Basic Report Information
  title: { type: String, required: true },
  description: { type: String, required: true },
  category: {
    type: String,
    enum: [
      'Financial Misconduct',
      'Accounting Fraud / Financial Manipulation',
      'Fraud',
      'Corruption',
      'Harassment',
      'Safety Violation',
      'Environmental',
      'Discrimination',
      'Other'
    ],
    required: true
  },
  dateOfOccurrence: { type: Date },
  location: { type: String },
  isAnonymous: { type: Boolean, default: false },

  // Step 2 - Detailed Information
  incidentDate: { type: Date },
  incidentTime: { type: String },
  specificLocation: { type: String },
  departmentInvolved: { type: String },
  peopleInvolved: { type: String },

  // Witness Information
  hasWitnesses: { type: Boolean, default: false },
  witnessDetails: { type: String },

  // Evidence Information
  hasEvidence: { type: Boolean, default: false },
  evidenceDescription: { type: String },
  evidenceFiles: [EvidenceFileSchema],

  // Impact Assessment
  urgencyLevel: {
    type: String,
    enum: ['Low', 'Medium', 'High', 'Critical'],
    default: 'Medium'
  },
  financialImpact: { type: String },
  operationalImpact: { type: String },
  reputationalImpact: { type: String },

  // Previous Reports
  hasPreviousReports: { type: Boolean, default: false },
  previousReportDetails: { type: String },

  // Additional Information
  additionalComments: { type: String },

  // Communication Preferences
  emailUpdates: { type: Boolean, default: true },
  smsUpdates: { type: Boolean, default: false },

  // System Fields
  priority: {
    type: String,
    enum: ['Low', 'Medium', 'High', 'Critical'],
    required: true
  },
  status: {
    type: String,
    enum: ['Draft', 'New', 'Under Review', 'Awaiting Response', 'Resolved', 'Closed'],
    default: 'New'
  },
  isDraft: { type: Boolean, default: false },
  dateSubmitted: { type: Date },
  submittedAt: { type: Date },

  // Draft Management
  draftStep: { type: Number, min: 1, max: 3 },
  lastSavedAt: { type: Date },

  // Legacy fields (for backward compatibility)
  evidence: [EvidenceFileSchema], // Keep for backward compatibility

  // Investigation Management
  assignedInvestigator: { type: Schema.Types.ObjectId, ref: 'User' },
  progress: { type: Number, default: 0, min: 0, max: 100 },
  estimatedCompletion: { type: Date },
  tags: [{ type: String }],

  // Metadata
  metadata: {
    ipAddress: { type: String },
    userAgent: { type: String },
    submissionMethod: {
      type: String,
      enum: ['web', 'mobile', 'api'],
      default: 'web'
    }
  }
}, {
  timestamps: true
});

// Generate reference number before saving
ReportSchema.pre('save', function(next) {
  if (this.isNew && !this.referenceNumber) {
    // Generate reference number: RPT-YYYYMMDD-XXXXX
    const date = new Date();
    const dateStr = date.getFullYear().toString() +
                   (date.getMonth() + 1).toString().padStart(2, '0') +
                   date.getDate().toString().padStart(2, '0');
    const randomStr = Math.random().toString(36).substr(2, 5).toUpperCase();
    this.referenceNumber = `RPT-${dateStr}-${randomStr}`;
  }
  next();
});

// Use the utility function to create the model safely
const Report = createModel('Report', ReportSchema);

export default Report;