"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";

import Header from "@/components/dashboard-components/Header";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { 
  Shield, 
  Home, 
  CheckCircle, 
  Calendar, 
  MapPin, 
  Users, 
  FileText, 
  AlertTriangle,
  Clock,
  Building,
  Mail,
  Phone,
  Edit,
  Send
} from "lucide-react";
import { NotificationEvents } from "@/lib/utils/notificationEvents";
import { apiClient } from "@/lib/utils/apiClient";

interface ReviewData {
  // Step 1 data
  title: string;
  category: string;
  dateOfOccurrence: string;
  location: string;
  description: string;
  isAnonymous: boolean;
  reportingPreferences: {
    emailUpdates: boolean;
    smsUpdates: boolean;
  };
  
  // Step 2 data
  incidentDate: string;
  incidentTime: string;
  specificLocation: string;
  departmentInvolved: string;
  peopleInvolved: string;
  witnessInfo: {
    hasWitnesses: boolean;
    witnessDetails: string;
  };
  evidenceInfo: {
    hasEvidence: boolean;
    evidenceDescription: string;
    evidenceFiles: File[];
  };
  impactAssessment: {
    financialImpact: string;
    operationalImpact: string;
    reputationalImpact: string;
  };
  previousReports: {
    hasPreviousReports: boolean;
    previousReportDetails: string;
  };
  urgencyLevel: string;
  additionalComments: string;
}

export default function Step3Page() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [reviewData, setReviewData] = useState<ReviewData | null>(null);
  const [confirmSubmission, setConfirmSubmission] = useState(false);

  useEffect(() => {
    // Load data from both steps
    const step1Data = sessionStorage.getItem('reportStep1Data');
    const step2Data = sessionStorage.getItem('reportStep2Data');
    
    if (step1Data && step2Data) {
      const step1Info = JSON.parse(step1Data);
      const step2Info = JSON.parse(step2Data);
      
      setReviewData({
        ...step1Info,
        ...step2Info
      });
    } else {
      // Redirect back if missing data
      toast.error('Missing report data. Please start from the beginning.');
      router.push('/dashboard/whistleblower/new-report');
    }
  }, [router]);

  const handleEditStep1 = () => {
    router.push('/dashboard/whistleblower/new-report');
  };

  const handleEditStep2 = () => {
    router.push('/dashboard/whistleblower/new-report/step-2');
  };

  const handleSubmitReport = async () => {
    if (!confirmSubmission) {
      toast.error('Please confirm that you want to submit this report');
      return;
    }

    if (!reviewData) {
      toast.error('No report data found');
      return;
    }

    setLoading(true);
    try {
      const result = await apiClient.post('/api/reports/submit', {
        ...reviewData,
        status: 'New',
        priority: reviewData.urgencyLevel || 'Medium',
        submittedAt: new Date().toISOString()
      });

      if (result.success) {
        toast.success('Report submitted successfully');

        // Trigger notification event for real-time updates
        const reportData = result.data as { _id?: string; id?: string };
        NotificationEvents.reportSubmitted(
          reportData._id || reportData.id || '',
          reviewData.title
        );

        // Clear session storage
        sessionStorage.removeItem('reportStep1Data');
        sessionStorage.removeItem('reportStep2Data');

        router.push('/dashboard/whistleblower/my-reports');
      } else {
        toast.error(result.error || 'Failed to submit report');
      }
    } catch (error) {
      console.error('Submit error:', error);
      toast.error('Failed to submit report');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveDraft = async () => {
    if (!reviewData) {
      toast.error('No report data found');
      return;
    }

    setLoading(true);
    try {
      const result = await apiClient.post('/api/reports/draft', {
        ...reviewData,
        status: 'Draft',
        step: 3
      });

      if (result.success) {
        toast.success('Draft saved successfully');
        router.push('/dashboard/whistleblower/my-reports');
      } else {
        toast.error(result.error || 'Failed to save draft');
      }
    } catch (error) {
      console.error('Save draft error:', error);
      toast.error('Failed to save draft');
    } finally {
      setLoading(false);
    }
  };

  if (!reviewData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-[#1E4841] mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="container mx-auto px-4 py-6">
        <div className="mb-6">
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink href="/dashboard/whistleblower">
                  <Home className="w-4 h-4" />
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink href="/dashboard/whistleblower/my-reports">
                  My Reports
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink href="/dashboard/whistleblower/new-report">
                  New Report
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage>Review & Submit</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              Review & Submit Report
            </h1>
            <p className="text-gray-600">
              Please review all information before submitting your report
            </p>
          </div>

          {/* Progress Indicator */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="flex items-center justify-center w-8 h-8 bg-green-600 text-white rounded-full">
                  ✓
                </div>
                <span className="ml-2 text-sm font-medium text-green-600">Report Details</span>
              </div>
              <div className="flex-1 h-1 bg-green-600 mx-4"></div>
              <div className="flex items-center">
                <div className="flex items-center justify-center w-8 h-8 bg-green-600 text-white rounded-full">
                  ✓
                </div>
                <span className="ml-2 text-sm font-medium text-green-600">Additional Details</span>
              </div>
              <div className="flex-1 h-1 bg-green-600 mx-4"></div>
              <div className="flex items-center">
                <div className="flex items-center justify-center w-8 h-8 bg-[#1E4841] text-white rounded-full">
                  3
                </div>
                <span className="ml-2 text-sm font-medium text-[#1E4841]">Review & Submit</span>
              </div>
            </div>
          </div>

          <Alert className="mb-6 border-blue-200 bg-blue-50">
            <Shield className="w-4 h-4 text-blue-600" />
            <AlertDescription className="text-blue-800">
              <strong>Final Review:</strong> Please carefully review all information below. Once submitted, your report will be processed and you will receive a confirmation with a tracking number.
            </AlertDescription>
          </Alert>

          {/* Report Summary Card */}
          <Card className="mb-6">
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="flex items-center">
                <FileText className="w-5 h-5 mr-2" />
                Report Summary
              </CardTitle>
              <Button
                variant="outline"
                size="sm"
                onClick={handleEditStep1}
              >
                <Edit className="w-4 h-4 mr-1" />
                Edit
              </Button>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label className="text-sm font-medium text-gray-600">Report Title</Label>
                <p className="text-sm mt-1">{reviewData.title}</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-600">Category</Label>
                  <p className="text-sm mt-1">
                    <Badge variant="secondary">{reviewData.category}</Badge>
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">Date of Occurrence</Label>
                  <p className="text-sm mt-1">{reviewData.dateOfOccurrence || 'Not specified'}</p>
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium text-gray-600">Location</Label>
                <p className="text-sm mt-1">{reviewData.location || 'Not specified'}</p>
              </div>

              <div>
                <Label className="text-sm font-medium text-gray-600">Description</Label>
                <p className="text-sm mt-1 bg-gray-50 p-3 rounded border">
                  {reviewData.description}
                </p>
              </div>

              <div>
                <Label className="text-sm font-medium text-gray-600">Anonymous Report</Label>
                <p className="text-sm mt-1">
                  <Badge variant={reviewData.isAnonymous ? "destructive" : "default"}>
                    {reviewData.isAnonymous ? "Yes - Anonymous" : "No - Identified"}
                  </Badge>
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Incident Details Card */}
          <Card className="mb-6">
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="flex items-center">
                <Calendar className="w-5 h-5 mr-2" />
                Incident Details
              </CardTitle>
              <Button
                variant="outline"
                size="sm"
                onClick={handleEditStep2}
              >
                <Edit className="w-4 h-4 mr-1" />
                Edit
              </Button>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-600">Incident Date</Label>
                  <p className="text-sm mt-1">{reviewData.incidentDate || 'Not specified'}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">Incident Time</Label>
                  <p className="text-sm mt-1">{reviewData.incidentTime || 'Not specified'}</p>
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium text-gray-600">Specific Location</Label>
                <p className="text-sm mt-1">{reviewData.specificLocation || 'Not specified'}</p>
              </div>

              <div>
                <Label className="text-sm font-medium text-gray-600">Department Involved</Label>
                <p className="text-sm mt-1">
                  {reviewData.departmentInvolved ? (
                    <Badge variant="outline">{reviewData.departmentInvolved}</Badge>
                  ) : (
                    'Not specified'
                  )}
                </p>
              </div>

              {reviewData.peopleInvolved && (
                <div>
                  <Label className="text-sm font-medium text-gray-600">People Involved</Label>
                  <p className="text-sm mt-1 bg-gray-50 p-3 rounded border">
                    {reviewData.peopleInvolved}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Witnesses Card */}
          {reviewData.witnessInfo.hasWitnesses && (
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Users className="w-5 h-5 mr-2" />
                  Witness Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div>
                  <Label className="text-sm font-medium text-gray-600">Witness Details</Label>
                  <p className="text-sm mt-1 bg-gray-50 p-3 rounded border">
                    {reviewData.witnessInfo.witnessDetails}
                  </p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Evidence Card */}
          {reviewData.evidenceInfo.hasEvidence && (
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FileText className="w-5 h-5 mr-2" />
                  Evidence & Documentation
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label className="text-sm font-medium text-gray-600">Evidence Description</Label>
                  <p className="text-sm mt-1 bg-gray-50 p-3 rounded border">
                    {reviewData.evidenceInfo.evidenceDescription}
                  </p>
                </div>

                {reviewData.evidenceInfo.evidenceFiles.length > 0 && (
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Uploaded Files</Label>
                    <div className="mt-2 space-y-2">
                      {reviewData.evidenceInfo.evidenceFiles.map((file, index) => (
                        <div key={index} className="flex items-center bg-gray-50 p-2 rounded border">
                          <FileText className="w-4 h-4 mr-2 text-gray-500" />
                          <span className="text-sm">{file.name}</span>
                          <span className="text-xs text-gray-500 ml-auto">
                            {(file.size / 1024 / 1024).toFixed(2)} MB
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Impact Assessment Card */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertTriangle className="w-5 h-5 mr-2" />
                Impact Assessment
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label className="text-sm font-medium text-gray-600">Urgency Level</Label>
                <p className="text-sm mt-1">
                  <Badge
                    variant={
                      reviewData.urgencyLevel === 'Critical' ? 'destructive' :
                      reviewData.urgencyLevel === 'High' ? 'destructive' :
                      reviewData.urgencyLevel === 'Medium' ? 'default' : 'secondary'
                    }
                  >
                    {reviewData.urgencyLevel}
                  </Badge>
                </p>
              </div>

              {(reviewData.impactAssessment.financialImpact ||
                reviewData.impactAssessment.operationalImpact ||
                reviewData.impactAssessment.reputationalImpact) && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {reviewData.impactAssessment.financialImpact && (
                    <div>
                      <Label className="text-sm font-medium text-gray-600">Financial Impact</Label>
                      <p className="text-sm mt-1 bg-gray-50 p-2 rounded border">
                        {reviewData.impactAssessment.financialImpact}
                      </p>
                    </div>
                  )}
                  {reviewData.impactAssessment.operationalImpact && (
                    <div>
                      <Label className="text-sm font-medium text-gray-600">Operational Impact</Label>
                      <p className="text-sm mt-1 bg-gray-50 p-2 rounded border">
                        {reviewData.impactAssessment.operationalImpact}
                      </p>
                    </div>
                  )}
                  {reviewData.impactAssessment.reputationalImpact && (
                    <div>
                      <Label className="text-sm font-medium text-gray-600">Reputational Impact</Label>
                      <p className="text-sm mt-1 bg-gray-50 p-2 rounded border">
                        {reviewData.impactAssessment.reputationalImpact}
                      </p>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Previous Reports Card */}
          {reviewData.previousReports.hasPreviousReports && (
            <Card className="mb-6">
              <CardHeader>
                <CardTitle>Previous Reports</CardTitle>
              </CardHeader>
              <CardContent>
                <div>
                  <Label className="text-sm font-medium text-gray-600">Previous Report Details</Label>
                  <p className="text-sm mt-1 bg-gray-50 p-3 rounded border">
                    {reviewData.previousReports.previousReportDetails}
                  </p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Additional Comments Card */}
          {reviewData.additionalComments && (
            <Card className="mb-6">
              <CardHeader>
                <CardTitle>Additional Comments</CardTitle>
              </CardHeader>
              <CardContent>
                <div>
                  <Label className="text-sm font-medium text-gray-600">Additional Information</Label>
                  <p className="text-sm mt-1 bg-gray-50 p-3 rounded border">
                    {reviewData.additionalComments}
                  </p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Reporting Preferences Card */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Mail className="w-5 h-5 mr-2" />
                Communication Preferences
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center">
                  <CheckCircle className={`w-4 h-4 mr-2 ${reviewData.reportingPreferences.emailUpdates ? 'text-green-600' : 'text-gray-400'}`} />
                  <span className="text-sm">Email updates</span>
                </div>
                <div className="flex items-center">
                  <CheckCircle className={`w-4 h-4 mr-2 ${reviewData.reportingPreferences.smsUpdates ? 'text-green-600' : 'text-gray-400'}`} />
                  <span className="text-sm">SMS updates</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Submission Confirmation */}
          <Card className="mb-6 border-yellow-200 bg-yellow-50">
            <CardHeader>
              <CardTitle className="text-yellow-800">Submission Confirmation</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-start space-x-2">
                <Checkbox
                  id="confirmSubmission"
                  checked={confirmSubmission}
                  onCheckedChange={(checked) => setConfirmSubmission(checked as boolean)}
                />
                <Label htmlFor="confirmSubmission" className="text-sm text-yellow-800">
                  I confirm that all the information provided is accurate to the best of my knowledge and I understand that this report will be investigated according to company policy. I also acknowledge that providing false information may result in disciplinary action.
                </Label>
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-between">
            <Button
              variant="outline"
              onClick={handleEditStep2}
              disabled={loading}
            >
              ← Back to Previous Step
            </Button>

            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                variant="outline"
                onClick={handleSaveDraft}
                disabled={loading}
              >
                Save as Draft
              </Button>
              <Button
                onClick={handleSubmitReport}
                disabled={loading || !confirmSubmission}
                className="bg-[#1E4841] hover:bg-[#2A5D54]"
              >
                <Send className="w-4 h-4 mr-2" />
                Submit Report
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
